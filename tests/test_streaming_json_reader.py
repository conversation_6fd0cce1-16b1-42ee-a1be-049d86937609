#!/usr/bin/env python3
"""
Comprehensive test suite for the streaming JSON reader DuckDB extension.
Tests various JSON structures, edge cases, and performance characteristics.
"""

import duckdb
import json
import time
import os
import sys

def setup_duckdb():
    """Setup DuckDB connection and load the extension."""
    conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
    
    # Load the extension
    extension_path = "build/debug/streaming_json_reader.duckdb_extension"
    if not os.path.exists(extension_path):
        print(f"ERROR: Extension not found at {extension_path}")
        print("Please run 'make debug' first to build the extension")
        sys.exit(1)
    
    conn.execute(f'LOAD "{extension_path}"')
    return conn

def test_simple_object():
    """Test reading a simple JSON object."""
    print("🧪 Testing simple JSON object...")
    conn = setup_duckdb()
    
    try:
        result = conn.execute('SELECT * FROM streaming_json_reader("tests/test_simple_object.json")').fetchall()
        columns = conn.execute('DESCRIBE SELECT * FROM streaming_json_reader("tests/test_simple_object.json")').fetchall()
        
        print(f"  ✅ Query succeeded with {len(result)} rows")
        print(f"  📊 Columns: {[(col[0], col[1]) for col in columns]}")
        if result:
            print(f"  📄 Data: {result[0]}")
        
        return True
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_multiply_nested():
    """Test reading multiply nested JSON structure."""
    print("🧪 Testing multiply nested JSON...")
    conn = setup_duckdb()
    
    try:
        result = conn.execute('SELECT * FROM streaming_json_reader("tests/test_multiply_nested.json")').fetchall()
        columns = conn.execute('DESCRIBE SELECT * FROM streaming_json_reader("tests/test_multiply_nested.json")').fetchall()
        
        print(f"  ✅ Query succeeded with {len(result)} rows")
        print(f"  📊 Columns: {[(col[0], col[1]) for col in columns]}")
        
        if result:
            metadata, users = result[0]
            print(f"  📄 Metadata: {metadata}")
            print(f"  👥 Users count: {len(users) if isinstance(users, list) else 'Not a list'}")
            
            # Test STRUCT access
            if isinstance(metadata, dict):
                print(f"  🏗️  STRUCT access - name: {metadata.get('name')}")
                print(f"  🏗️  STRUCT access - version: {metadata.get('version')}")
            
            # Test ARRAY access
            if isinstance(users, list) and users:
                print(f"  📋 ARRAY access - first user: {users[0][:100]}...")
        
        return True
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_array_of_primitives():
    """Test reading arrays of primitive types."""
    print("🧪 Testing arrays of primitives...")
    conn = setup_duckdb()
    
    try:
        result = conn.execute('SELECT * FROM streaming_json_reader("tests/test_array_of_primitives.json")').fetchall()
        columns = conn.execute('DESCRIBE SELECT * FROM streaming_json_reader("tests/test_array_of_primitives.json")').fetchall()
        
        print(f"  ✅ Query succeeded with {len(result)} rows")
        print(f"  📊 Columns: {[(col[0], col[1]) for col in columns]}")
        
        if result:
            print(f"  📄 First row: {result[0]}")
            if len(result) > 1:
                print(f"  📄 Second row: {result[1]}")
        
        return True
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_large_numbers():
    """Test reading various number formats."""
    print("🧪 Testing large numbers and number formats...")
    conn = setup_duckdb()
    
    try:
        result = conn.execute('SELECT * FROM streaming_json_reader("tests/test_large_numbers.json")').fetchall()
        columns = conn.execute('DESCRIBE SELECT * FROM streaming_json_reader("tests/test_large_numbers.json")').fetchall()
        
        print(f"  ✅ Query succeeded with {len(result)} rows")
        print(f"  📊 Columns: {[(col[0], col[1]) for col in columns]}")
        
        if result:
            print(f"  📄 Numbers: {result[0]}")
        
        return True
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_performance_comparison():
    """Compare performance with DuckDB's default JSON reader."""
    print("🧪 Testing performance comparison...")
    conn = setup_duckdb()
    
    try:
        # Test our streaming reader
        start_time = time.time()
        result1 = conn.execute('SELECT * FROM streaming_json_reader("tests/test_multiply_nested.json")').fetchall()
        streaming_time = time.time() - start_time
        
        # Test DuckDB's default JSON reader
        start_time = time.time()
        result2 = conn.execute('SELECT * FROM read_json("tests/test_multiply_nested.json")').fetchall()
        default_time = time.time() - start_time
        
        print(f"  ⚡ Streaming reader: {streaming_time:.4f}s")
        print(f"  ⚡ Default reader: {default_time:.4f}s")
        print(f"  📊 Results match: {len(result1) == len(result2)}")
        
        return True
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_projection_pushdown():
    """Test column projection capabilities."""
    print("🧪 Testing projection pushdown...")
    conn = setup_duckdb()
    
    try:
        # Test selecting only metadata
        result1 = conn.execute('SELECT metadata FROM streaming_json_reader("tests/test_multiply_nested.json")').fetchall()
        print(f"  ✅ Metadata only: {len(result1)} rows")
        
        # Test selecting only users
        result2 = conn.execute('SELECT users FROM streaming_json_reader("tests/test_multiply_nested.json")').fetchall()
        print(f"  ✅ Users only: {len(result2)} rows")
        
        # Test selecting both
        result3 = conn.execute('SELECT metadata, users FROM streaming_json_reader("tests/test_multiply_nested.json")').fetchall()
        print(f"  ✅ Both columns: {len(result3)} rows")
        
        return True
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def run_all_tests():
    """Run all tests and report results."""
    print("🚀 Starting comprehensive test suite for streaming JSON reader extension\n")
    
    tests = [
        ("Simple Object", test_simple_object),
        ("Multiply Nested", test_multiply_nested),
        ("Array of Primitives", test_array_of_primitives),
        ("Large Numbers", test_large_numbers),
        ("Performance Comparison", test_performance_comparison),
        ("Projection Pushdown", test_projection_pushdown),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"TEST: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
    
    print(f"\n{'='*50}")
    print(f"TEST SUMMARY")
    print('='*50)
    print(f"Passed: {passed}/{total}")
    print(f"Success rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        return True
    else:
        print("⚠️  Some tests failed")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
