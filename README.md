# DuckDB Streaming JSON Reader Extension

A JSON reader extension for DuckDB implemented in Rust using the struson streaming parser.

## Current Capabilities (Verified by Tests)

- **Projection Pushdown**: Skips unrequested JSON fields during parsing (test verified)
- **Error Handling**: Proper error messages for malformed JSON files (test verified)
- **Basic JSON Parsing**: Reads simple JSON objects and creates table structures
- **STRUCT Creation**: Creates STRUCT types for nested JSON objects
- **String Data Types**: Correctly handles VARCHAR fields

## Quick Start

### Build the Extension

```shell
make configure
make debug
```

### Load and Use

```python
import duckdb
conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')

# Query JSON file
result = conn.execute('SELECT * FROM streaming_json_reader("path/to/file.json")').fetchall()
```

### Basic Usage Examples

```sql
-- Select all columns from JSON file
SELECT * FROM streaming_json_reader('data.json');

-- Select specific fields (projection pushdown working)
SELECT metadata FROM streaming_json_reader('nested.json');
```

## Known Issues (Test Results)

### Critical Failures
- **Data Types**: Numbers stored as VARCHAR instead of DOUBLE/INTEGER
- **Deep Nesting**: Extension crashes on JSON with 2+ levels of nesting
- **STRUCT Values**: Numeric values within STRUCTs stored as strings
- **Array Processing**: Untested due to blocking crashes

### Specific Test Failures
- `test_simple_object_basic_types`: Numbers have wrong data type (VARCHAR vs DOUBLE)
- `test_struct_field_access`: STRUCT field values are strings instead of proper types
- `test_nested_struct_access`: Fatal crash on deep nesting

## Test Verification

Run comprehensive test suite:
```shell
uv run pytest tests/test_streaming_json_reader_comprehensive.py -v
```

Current test results: Multiple core functionality failures identified.

## Dependencies

- Rust toolchain
- Python3 and Python3-venv
- [Make](https://www.gnu.org/software/make)
- Git

## Building

```shell
# Configure build environment
make configure

# Build debug version
make debug

# Build release version  
make release
```

The build process creates `build/debug/streaming_json_reader.duckdb_extension` which can be loaded into DuckDB.

## Testing

Run the test suite:

```shell
# Run debug tests
make test_debug

# Run release tests  
make test_release
```

### Manual Testing

```python
import duckdb
conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')

# Test with sample data
conn.execute('SELECT * FROM streaming_json_reader("tests/test_multiply_nested.json")').fetchall()
```

## Current Status

### ✅ Implemented
- Recursive JSON schema discovery
- Path-based column generation  
- DuckDB table function integration
- Projection pushdown support
- Basic streaming infrastructure

### 🚧 Critical Issues (In Progress)
1. **Incorrect JSON Representation**: Currently flattening JSON into top-level columns instead of preserving structure as STRUCTs/ARRAYs
2. **Number Type Display**: Scientific notation instead of clean integers/floats
3. **Streaming Implementation**: Not yet using generated paths for efficient parsing
4. **Memory Optimization**: Not achieving memory efficiency goals

### 📋 Next Steps
1. Fix JSON schema generation to create STRUCT types for objects and ARRAY types for arrays (like DuckDB's default JSON reader)
2. Implement true streaming that only parses projected JSON segments
3. Add memory usage comparison tests vs default DuckDB JSON reader
4. Performance optimization and benchmarking

## Architecture

See [design_decisions.md](design_decisions.md) for detailed architectural decisions and implementation approaches.
